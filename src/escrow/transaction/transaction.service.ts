import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  TransactionRepository,
  UserRepository,
  PaymentMethodRepository,
} from 'src/common/repository/';
import { CreateTransactionDto } from './dto/create.dto';
import { Transaction, NewTransaction, User } from 'src/common/schemas';
import { EmailsService } from 'src/core/emails/emails.service';
import { NotificationsService } from 'src/core/notifications/notifications.service';
import {
  PaginatedResponse,
  TransactionStatus,
  TransactionFilter,
} from 'src/common/interfaces';

import { FilesService } from 'src/core/files/files.service';
import { FileRepository } from 'src/common/repository';
import { DeclineTransactionDto } from './dto/decline.dto';
import { AcceptTransactionDto } from './dto/accept.dto';

@Injectable()
export class TransactionService {
  constructor(
    private readonly transactionRepository: TransactionRepository,
    private readonly userRepository: UserRepository,
    private readonly paymentMethodRepository: PaymentMethodRepository,
    private readonly emailsService: EmailsService,
    private readonly notificationsService: NotificationsService,
    private readonly fileService: FilesService,
    private readonly fileRepository: FileRepository,
  ) {}

  async createTransaction(
    userId: number,
    createTransactionDto: CreateTransactionDto,
    attachments: Express.Multer.File[],
  ): Promise<Transaction> {
    try {
      const initiator = await this.userRepository.findById(userId);
      if (!initiator) throw new NotFoundException('Initiator user not found');

      const receiver = await this.userRepository.findById(
        createTransactionDto.sellerId,
      );
      if (!receiver) throw new NotFoundException('Receiver user not found');

      // Validate payment method exists and belongs to user
      const paymentMethod = await this.paymentMethodRepository.findById(
        createTransactionDto.paymentMethodId,
      );
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');

      if (paymentMethod.userId !== userId)
        throw new BadRequestException('Payment method does not belong to user');

      const transactionData: NewTransaction = {
        title: createTransactionDto.title,
        description: createTransactionDto.description,
        aggrement: createTransactionDto.aggrement,
        category: createTransactionDto.category,
        deadLine: new Date(createTransactionDto.deadLine),
        initiatedBy: userId,
        receivedBy: createTransactionDto.sellerId,
        paymentMethodId: createTransactionDto.paymentMethodId,
        amount: createTransactionDto.amount,
        status: 'pending',
      };

      const transaction =
        await this.transactionRepository.create(transactionData);

      if (attachments && attachments.length > 0) {
        const uploadedFiles =
          await this.fileService.uploadMultipleFiles(attachments);

        const filePromises = uploadedFiles.map((file) =>
          this.fileRepository.create({
            name: file.original_filename,
            url: file.url,
            transactionId: transaction.id,
          }),
        );

        await Promise.all(filePromises);
      }

      // Send notifications and emails
      await this.sendTransactionNotifications(transaction, initiator, receiver);

      return transaction;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to create transaction',
        error as Error,
      );
    }
  }

  private async sendTransactionNotifications(
    transaction: Transaction,
    initiator: User,
    receiver: User,
  ): Promise<void> {
    try {
      // Send email to receiver
      await this.emailsService.sendTransactionCreatedEmail(
        receiver.email,
        receiver.name,
        transaction.title,
        transaction.amount,
        false, // not initiator
      );

      // Send email to initiator
      await this.emailsService.sendTransactionCreatedEmail(
        initiator.email,
        initiator.name,
        transaction.title,
        transaction.amount,
        true, // is initiator
      );

      // Create notification for receiver
      await this.notificationsService.create({
        title: 'New Transaction Request',
        message: `${initiator.name} sent you a transaction request for "${transaction.title}"`,
        userId: receiver.id,
        type: 'personal',
        category: 'transaction',
        priority: 'high',
        metadata: {
          transactionId: transaction.id,
          initiatorId: initiator.id,
          amount: transaction.amount,
        },
      });

      // Create notification for initiator
      await this.notificationsService.create({
        title: 'Transaction Created',
        message: `Your transaction "${transaction.title}" has been created successfully`,
        userId: initiator.id,
        type: 'personal',
        category: 'transaction',
        priority: 'normal',
        metadata: {
          transactionId: transaction.id,
          receiverId: receiver.id,
          amount: transaction.amount,
        },
      });
    } catch (error) {
      // Log error but don't fail the transaction creation
      console.error('Failed to send notifications:', error);
    }
  }

  async getAllTransactions(
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      return await this.transactionRepository.findAll(query);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions',
        error as Error,
      );
    }
  }

  async declineTransaction(
    userId: number,
    declineTransactionDto: DeclineTransactionDto,
  ) {
    try {
      const transaction = await this.transactionRepository.findById(
        declineTransactionDto.transactionId,
      );
      if (!transaction) throw new NotFoundException('Transaction not found');

      if (transaction.receivedBy !== userId)
        throw new BadRequestException(
          'Only the transaction initiator can decline the transaction',
        );

      if (transaction.status !== 'pending')
        throw new BadRequestException(
          'Only transactions with "pending" status can be declined',
        );

      const updatedTransaction = await this.transactionRepository.update(
        transaction.id,
        { status: 'declined' },
      );

      // await this.sendStatusUpdateNotifications(
      //   updatedTransaction,
      //   'declined',
      //   userId,
      // );

      return updatedTransaction;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to decline transaction',
        error as Error,
      );
    }
  }

  async getTransactionById(id: number): Promise<Transaction> {
    try {
      const transaction = await this.transactionRepository.findById(id);
      if (!transaction) {
        throw new NotFoundException('Transaction not found');
      }
      return transaction;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to fetch transaction',
        error as Error,
      );
    }
  }

  async getUserTransactions(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction> | null> {
    try {
      return await this.transactionRepository.findByUserId(userId, query);
    } catch (error) {
      console.log(error);

      throw new InternalServerErrorException(
        'Failed to fetch user transactions',
        error as Error,
      );
    }
  }

  async getReceivedTransactions(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction> | null> {
    try {
      return await this.transactionRepository.findByReceivedBy(userId, query);
    } catch (error) {
      console.log(error);

      throw new InternalServerErrorException(
        'Failed to fetch received transactions',
        error as Error,
      );
    }
  }

  async updateTransactionStatus(
    id: number,
    status: TransactionStatus,
    userId?: number,
  ) {
    try {
      const transaction = await this.transactionRepository.findById(id);
      if (!transaction) {
        throw new NotFoundException('Transaction not found');
      }

      const updatedTransaction = await this.transactionRepository.update(id, {
        status: status,
      });

      if (!updatedTransaction) {
        throw new InternalServerErrorException('Failed to update transaction');
      }

      // Send notifications for status updates
      await this.sendStatusUpdateNotifications(
        updatedTransaction,
        status,
        userId,
      );

      return updatedTransaction;
    } catch (error) {
      new InternalServerErrorException(
        'Failed to update transaction status',
        error as Error,
      );
    }
  }

  private async sendStatusUpdateNotifications(
    transaction: Transaction,
    status: string,
    updatedBy?: number,
  ): Promise<void> {
    try {
      const initiator = await this.userRepository.findById(
        transaction.initiatedBy!,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy!,
      );

      if (!initiator || !receiver) return;

      const statusMessages = {
        completed: 'has been completed successfully',
        disputed: 'is under dispute',
        cancelled: 'has been cancelled',
        expired: 'has expired',
        failed: 'has failed',
      };

      const message =
        statusMessages[status as keyof typeof statusMessages] ||
        `status has been updated to ${status}`;

      // Send emails to both parties
      await this.emailsService.sendTransactionStatusUpdateEmail(
        initiator.email,
        initiator.name,
        transaction.title,
        status,
      );

      await this.emailsService.sendTransactionStatusUpdateEmail(
        receiver.email,
        receiver.name,
        transaction.title,
        status,
      );

      // Create notifications for both parties
      await this.notificationsService.create({
        title: 'Transaction Status Update',
        message: `Transaction "${transaction.title}" ${message}`,
        userId: initiator.id,
        type: 'personal',
        category: 'transaction',
        priority: status === 'disputed' ? 'high' : 'normal',
        metadata: {
          transactionId: transaction.id,
          status,
          updatedBy,
        },
      });

      await this.notificationsService.create({
        title: 'Transaction Status Update',
        message: `Transaction "${transaction.title}" ${message}`,
        userId: receiver.id,
        type: 'personal',
        category: 'transaction',
        priority: status === 'disputed' ? 'high' : 'normal',
        metadata: {
          transactionId: transaction.id,
          status,
          updatedBy,
        },
      });
    } catch (error) {
      console.error('Failed to send status update notifications:', error);
    }
  }

  async deleteTransaction(id: number, userId: number): Promise<boolean> {
    try {
      const transaction = await this.transactionRepository.findById(id);
      if (!transaction) {
        throw new NotFoundException('Transaction not found');
      }

      if (transaction.initiatedBy !== userId)
        throw new BadRequestException(
          'Only the transaction initiator can delete the transaction',
        );

      if (transaction.status !== 'pending')
        throw new BadRequestException(
          'Only transactions with "pending" status can be deleted',
        );

      const deleted = await this.transactionRepository.delete(id);

      if (deleted) {
        const receiver = await this.userRepository.findById(
          transaction.receivedBy!,
        );
        const initiator = await this.userRepository.findById(
          transaction.initiatedBy,
        );

        if (receiver && initiator) {
          await this.emailsService.sendTransactionCancelledEmail(
            receiver.email,
            receiver.name,
            transaction.title,
            initiator.name,
          );

          await this.notificationsService.create({
            title: 'Transaction Cancelled',
            message: `Transaction "${transaction.title}" has been cancelled by ${initiator.name}`,
            userId: receiver.id,
            type: 'personal',
            category: 'transaction',
            priority: 'normal',
            metadata: {
              transactionId: transaction.id,
              cancelledBy: userId,
            },
          });
        }
      }

      return deleted;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to delete transaction',
        error as Error,
      );
    }
  }

  async acceptTransaction(
    userId: number,
    acceptTransactionDto: AcceptTransactionDto,
  ) {
    try {
      const transaction = await this.transactionRepository.findById(
        acceptTransactionDto.transactionId,
      );
      if (!transaction) throw new NotFoundException('Transaction not found');

      if (transaction.receivedBy !== userId)
        throw new BadRequestException(
          'Only the transaction receiver can accept the transaction',
        );

      if (transaction.status !== 'pending')
        throw new BadRequestException(
          'Only transactions with "pending" status can be accepted',
        );

      const updatedTransaction = await this.transactionRepository.update(
        transaction.id,
        { status: 'accepted' },
      );

      // await this.sendStatusUpdateNotifications(
      //   updatedTransaction,
      //   'completed',
      //   userId,
      // );

      return updatedTransaction;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to accept transaction',
        error as Error,
      );
    }
  }

  async getTransactionsByStatus(
    status: TransactionStatus,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = { ...query, status };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions by status',
        error as Error,
      );
    }
  }

  async getTransactionsByDateRange(
    startDate: string,
    endDate: string,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = {
        ...query,
        createdFrom: startDate,
        createdTo: endDate,
      };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions by date range',
        error as Error,
      );
    }
  }

  async getTransactionsByAmountRange(
    minAmount: number,
    maxAmount: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = {
        ...query,
        minAmount,
        maxAmount,
      };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions by amount range',
        error as Error,
      );
    }
  }

  async searchTransactions(
    searchTerm: string,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = { ...query, search: searchTerm };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to search transactions',
        error as Error,
      );
    }
  }

  async getTransactionsByCategory(
    category: string,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = { ...query, category };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions by category',
        error as Error,
      );
    }
  }

  async getTransactionsByPaymentMethod(
    paymentMethodId: number,
    query: TransactionFilter,
    userId: number,
  ) {
    try {
      const transactions =
        await this.transactionRepository.findByPaymentMethodId(
          paymentMethodId,
          userId,
          query,
        );
      return transactions;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions by payment method',
        error as Error,
      );
    }
  }

  async getStatusCount(userId: number) {
    try {
      return await this.transactionRepository.getStatusCount(userId);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transaction status count',
        error as Error,
      );
    }
  }
}
